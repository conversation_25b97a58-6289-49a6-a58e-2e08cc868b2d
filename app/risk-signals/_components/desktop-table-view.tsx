import { TrendChange } from "@/db/trend-change";
import { Table, Text, Badge } from "@radix-ui/themes";
import { ArrowUpIcon, ArrowDownIcon, DashIcon } from "@radix-ui/react-icons";
import { customNumberFormatter } from "@/lib/utils-client";
import OptionChainDialog from "./option-chain";
import OrderDialog from "@/app/portfolio/_components/order-dialog";
import WatchlistButton from "./watchlist-button";
import { HoldingsIndicator } from "@/app/_components/holdings-indicator";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";

interface DesktopTableViewProps {
  resultTableTrendChange: TrendChange[];
  showStockPicksOnly: boolean;
  stockPicks: TrendChange[];
  entryWindow: string;
  session: any;
  watchlistItems: string[];
  onToggleWatchlist: (symbol: string) => Promise<void>;
  holdings: PortfolioPosition[] | null;
}

export default function DesktopTableView({
  resultTableTrendChange,
  showStockPicksOnly,
  stockPicks,
  entryWindow,
  session,
  watchlistItems,
  onToggleWatchlist,
  holdings,
}: DesktopTableViewProps) {
  console.log(
    "DesktopTableView() called: resultTableTrendChange.length ->",
    resultTableTrendChange.length,
  );
  console.log("DesktopTableView: showStockPicksOnly", showStockPicksOnly);
  return (
    <div className="hidden md:block h-[calc(100vh-300px)] overflow-auto">
      <Table.Root variant="surface" className="relative">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell className="sticky left-0 z-10 bg-inherit after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-800">
              Symbol
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Trend</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">Buy</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">Sell</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">
              Previous
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">
              Window
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Action</Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {resultTableTrendChange.map((r) => (
            <Table.Row
              key={r.index}
              className={`
                ${
                  stockPicks.some((s) => s.index === r.index)
                    ? "dark:bg-green-900/40 bg-green-50"
                    : ""
                }
                hover:dark:bg-slate-800 hover:bg-slate-50
                transition-colors
              `}
            >
              <Table.RowHeaderCell className="sticky left-0 z-10 bg-inherit after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-800">
                <div className="flex items-center gap-2">
                  <Text weight="bold">{r.index}</Text>
                  <HoldingsIndicator symbol={r.index} holdings={holdings} />
                </div>
              </Table.RowHeaderCell>
              <Table.Cell>
                <Badge
                  variant="soft"
                  color={
                    r.trend === "BULLISH"
                      ? "green"
                      : r.trend === "BEARISH"
                        ? "red"
                        : "blue"
                  }
                  className="flex items-center gap-1"
                >
                  {r.trend === "BULLISH" ? (
                    <ArrowUpIcon />
                  ) : r.trend === "BEARISH" ? (
                    <ArrowDownIcon />
                  ) : (
                    <DashIcon />
                  )}
                  {r.trend}
                </Badge>
              </Table.Cell>
              <Table.Cell align="right">
                {customNumberFormatter(r.buyTrade)}
              </Table.Cell>
              <Table.Cell align="right">
                {customNumberFormatter(r.sellTrade)}
              </Table.Cell>
              <Table.Cell align="right">
                {customNumberFormatter(r.previousClose)}
              </Table.Cell>
              <Table.Cell align="right">
                {customNumberFormatter(
                  (r.sellTrade - r.buyTrade) * (Number(entryWindow) / 100),
                )}
              </Table.Cell>
              <Table.Cell>
                <div className="flex items-center gap-2">
                  {session.user.role === "admin" &&
                    stockPicks.some((s) => s.index === r.index) && (
                      <div className="flex items-center gap-2">
                        <OrderDialog
                          userId={session.user.id}
                          symbol={r.index}
                          price={r.previousClose}
                          action={r.trend === "BULLISH" ? "BUY" : "SELL"}
                        />
                      </div>
                    )}
                  <WatchlistButton
                    symbol={r.index}
                    isInWatchlist={watchlistItems.includes(r.index)}
                    onToggleWatchlist={onToggleWatchlist}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </div>
  );
}
