"use client";

import { useEffect, useState } from "react";
import { getNYSEStatus } from "@/utils/yahoo-finance/is-market-open";

export function MarketStatusIndicator() {
  const [marketStatus, setMarketStatus] = useState<{
    isOpen: boolean;
    status: "pre-market" | "open" | "closed" | "after-hours";
  }>({ isOpen: false, status: "closed" });

  useEffect(() => {
    const updateMarketStatus = () => {
      setMarketStatus(getNYSEStatus());
    };

    // Update immediately
    updateMarketStatus();
    // Then update every minute
    const interval = setInterval(updateMarketStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    switch (marketStatus.status) {
      case "open":
        return "bg-green-500";
      case "pre-market":
        return "bg-yellow-500";
      case "after-hours":
        return "bg-orange-500";
      case "closed":
        return "bg-red-500";
    }
  };

  const getStatusText = () => {
    switch (marketStatus.status) {
      case "open":
        return "Market Open";
      case "pre-market":
        return "Pre-Market";
      case "after-hours":
        return "After Hours";
      case "closed":
        return "Market Closed";
    }
  };

  const getMobileStatusText = () => {
    switch (marketStatus.status) {
      case "open":
        return "Open";
      case "pre-market":
        return "Pre";
      case "after-hours":
        return "After";
      case "closed":
        return "Closed";
    }
  };

  return (
    <>
      {/* Desktop version */}
      <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-full border border-slate-200 dark:border-slate-700">
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
          {getStatusText()}
        </span>
      </div>

      {/* Mobile version */}
      <div className="sm:hidden flex items-center gap-1 px-2 py-1 rounded-full border border-slate-200 dark:border-slate-700">
        <div className={`w-1.5 h-1.5 rounded-full ${getStatusColor()}`} />
        <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
          {getMobileStatusText()}
        </span>
      </div>
    </>
  );
}
