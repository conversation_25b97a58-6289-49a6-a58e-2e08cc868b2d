"use client";

import { useState } from "react";
import Link from "next/link";
import { testIBKRConnection } from "@/app/profile/actions/ibkr-test-connection";
import { DashboardAnalytics } from "../actions/fetch-dashboard-analytics";
import { TrendChange } from "@/db/trend-change";

// Export the interface
export interface DashboardPageMainComponentProps {
  userId: string;
  initialDashboardData: DashboardAnalytics;
}

export default function DashboardPageMainComponent({
  userId,
  initialDashboardData,
}: DashboardPageMainComponentProps) {
  // Add state for the checkbox
  const [showStocksOnly, setShowStocksOnly] = useState(true);
  const [canConnectIBAccount, setCanConnectIBAccount] = useState(
    initialDashboardData.connection_status.is_connected,
  );
  const [dashboardData, setDashboardData] = useState(initialDashboardData);
  const [isRetrying, setIsRetrying] = useState(false);

  // Filter the symbols based on the checkbox
  const filteredSymbols = showStocksOnly
    ? dashboardData.symbols_within_10_percent_window_entry.filter(
        (item: { isStock: boolean }) => item.isStock,
      )
    : dashboardData.symbols_within_10_percent_window_entry;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Section with Connection Status */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Welcome back!</h1>
          <p className="text-gray-600">
            Here's what's happening with your portfolio
          </p>
        </div>

        {/* Connection Status Indicator */}
        <div className="mt-4 md:mt-0">
          <div
            className={`flex items-center gap-2 px-4 py-2 rounded-full border ${
              dashboardData.connection_status.is_connected
                ? "bg-green-50 border-green-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div
              className={`w-2 h-2 rounded-full animate-pulse ${
                dashboardData.connection_status.is_connected
                  ? "bg-green-500"
                  : "bg-red-500"
              }`}
            />
            <span
              className={`text-sm font-medium ${
                dashboardData.connection_status.is_connected
                  ? "text-green-700"
                  : "text-red-700"
              }`}
            >
              {dashboardData.connection_status.is_connected ? (
                <>
                  Connected to IBKR
                  <span className="ml-2 text-green-600 font-normal">
                    ({dashboardData.connection_status.account_id})
                  </span>
                </>
              ) : (
                "Disconnected from IBKR"
              )}
            </span>
            {!canConnectIBAccount && (
              <button
                className="ml-2 p-1 hover:bg-red-100 rounded-full transition-colors disabled:opacity-50"
                title="Retry connection"
                disabled={isRetrying}
                onClick={async () => {
                  setIsRetrying(true);
                  try {
                    const result = await testIBKRConnection(userId);
                    setCanConnectIBAccount(result);
                  } finally {
                    setIsRetrying(false);
                  }
                }}
              >
                <svg
                  className={`w-4 h-4 text-red-600 ${
                    isRetrying ? "animate-spin" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            )}
            <div
              className="ml-2 text-xs text-gray-500"
              title={new Date(
                dashboardData.connection_status.last_checked,
              ).toLocaleString()}
            >
              {getTimeAgo(dashboardData.connection_status.last_checked)}
            </div>
          </div>
        </div>
      </div>

      {/* Risk Signals Section */}
      <div className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Risk Signals Monitor
          </h2>
          <div className="mt-2 md:mt-0 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
            Last Updated:{" "}
            {new Date(
              dashboardData.risk_signals_last_updated,
            ).toLocaleDateString()}
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Trend Changes Alert */}
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="w-full">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-medium text-gray-700">
                    Trend Changes
                  </h3>
                  <span className="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium">
                    {
                      dashboardData.symbols_trend_changed_from_previous_date
                        .length
                    }{" "}
                    symbols
                  </span>
                </div>
                <p className="text-sm text-gray-500 mb-3">
                  Symbols changed trend
                </p>
                <div className="flex flex-col gap-2">
                  {dashboardData.symbols_trend_changed_from_previous_date.map(
                    (item: { current: TrendChange; previous: TrendChange }) => (
                      <div
                        key={item.current.index}
                        className="flex items-center justify-between p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {item.current.index}
                          </span>
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </div>
                        <div className="flex items-center gap-2">
                          <span
                            className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                              item.previous.trend === "BULLISH"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {item.previous.trend}
                          </span>
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 5l7 7-7 7M5 5l7 7-7 7"
                            />
                          </svg>
                          <span
                            className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                              item.current.trend === "BULLISH"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {item.current.trend}
                          </span>
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded-full h-fit ml-4">
                <svg
                  className="w-8 h-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Entry Window Opportunities */}
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="w-full">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-medium text-gray-700">
                    Entry Opportunities
                  </h3>
                  <label className="flex items-center space-x-2 text-sm text-gray-600">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-300 focus:ring-purple-500"
                      checked={showStocksOnly}
                      onChange={(e) => setShowStocksOnly(e.target.checked)}
                    />
                    <span>Stocks only</span>
                  </label>
                </div>
                <p className="text-3xl font-bold text-purple-600 mt-2">
                  {filteredSymbols.length}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Within 10% entry window
                </p>
                <div className="mt-3 flex flex-wrap gap-2">
                  {filteredSymbols.map((item: { symbol: string }) => (
                    <span
                      key={item.symbol}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                    >
                      {item.symbol}
                    </span>
                  ))}
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-full h-fit">
                <svg
                  className="w-8 h-8 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l3 3 3-3m0 0V3.6a9 9 0 11-6 0V12"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Risk Analysis Status */}
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-700">
                  Signal Status
                </h3>
                <p className="text-sm font-medium text-green-600 mt-2">
                  Download Complete
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Next update in{" "}
                  {getTimeUntilNextUpdate(
                    dashboardData.risk_signals_last_updated,
                  )}
                </p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid - Now with 4 cards */}
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Trading Overview
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Watchlist Card */}
        <Link
          href="/watchlist"
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Watchlist</h3>
              <span className="p-2 bg-blue-50 rounded-lg">
                <svg
                  className="w-6 h-6 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              </span>
            </div>
            <p className="text-2xl font-semibold text-gray-800">
              {dashboardData.stocks_in_watchlist}
            </p>
            <p className="text-sm text-gray-500">Stocks being tracked</p>
          </div>
        </Link>

        {/* Open Orders Card */}
        <Link
          href="/portfolio?tab=orders"
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Open Orders</h3>
              <span className="p-2 bg-yellow-50 rounded-lg">
                <svg
                  className="w-6 h-6 text-yellow-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </span>
            </div>
            <p className="text-2xl font-semibold text-gray-800">
              {dashboardData.trading_data.open_orders}
            </p>
            <p className="text-sm text-gray-500">Pending orders</p>
          </div>
        </Link>

        {/* Open Positions Card */}
        <Link
          href="/portfolio"
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">
                Open Positions
              </h3>
              <span className="p-2 bg-green-50 rounded-lg">
                <svg
                  className="w-6 h-6 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </span>
            </div>
            <p className="text-2xl font-semibold text-gray-800">
              {dashboardData.trading_data.open_positions}
            </p>
            <p className="text-sm text-gray-500">Active positions</p>
          </div>
        </Link>

        {/* Auto Prefill Orders Card */}
        <Link
          href="/portfolio?tab=auto-prefill"
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Auto Orders</h3>
              <span className="p-2 bg-indigo-50 rounded-lg">
                <svg
                  className="w-6 h-6 text-indigo-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </span>
            </div>
            <p className="text-2xl font-semibold text-gray-800">
              {dashboardData.trading_data.auto_prefill_orders}
            </p>
            <p className="text-sm text-gray-500">Pending auto orders</p>
          </div>
        </Link>
      </div>

      {/* Portfolio Performance Section */}
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Current Holdings Performance
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Best Performing Position */}
        <Link
          href={`/portfolio`}
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Best Performing Position
              </h3>
              <span className="px-3 py-1 bg-green-50 text-green-600 rounded-full text-sm font-medium">
                +
                {
                  dashboardData.trading_data.positions_performance.top_gainer
                    .gain_percentage
                }
                %
              </span>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-gray-800">
                    {
                      dashboardData.trading_data.positions_performance
                        .top_gainer.symbol
                    }
                  </p>
                  <p className="text-sm text-gray-500">
                    Position Size: $
                    {dashboardData.trading_data.positions_performance.top_gainer.position_size.toLocaleString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-green-500">
                    +$
                    {dashboardData.trading_data.positions_performance.top_gainer.gain?.toLocaleString() ||
                      "0"}
                  </p>
                  <p className="text-sm text-gray-500">Unrealized Gain</p>
                </div>
              </div>
            </div>
          </div>
        </Link>

        {/* Worst Performing Position */}
        <Link
          href={`/portfolio`}
          className="block transition-transform hover:scale-105"
        >
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Worst Performing Position
              </h3>
              <span className="px-3 py-1 bg-red-50 text-red-600 rounded-full text-sm font-medium">
                -
                {
                  dashboardData.trading_data.positions_performance.top_loser
                    .loss_percentage
                }
                %
              </span>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-gray-800">
                    {
                      dashboardData.trading_data.positions_performance.top_loser
                        .symbol
                    }
                  </p>
                  <p className="text-sm text-gray-500">
                    Position Size: $
                    {dashboardData.trading_data.positions_performance.top_loser.position_size.toLocaleString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-red-500">
                    -$
                    {dashboardData.trading_data.positions_performance.top_loser.loss?.toLocaleString() ||
                      "0"}
                  </p>
                  <p className="text-sm text-gray-500">Unrealized Loss</p>
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
}

// Helper function to calculate time until next update
function getTimeUntilNextUpdate(lastUpdated: string): string {
  const now = new Date();
  const currentUTC = new Date(now.toUTCString());

  // Set next update time to 14:00 UTC today
  const nextUpdate = new Date(
    Date.UTC(
      currentUTC.getUTCFullYear(),
      currentUTC.getUTCMonth(),
      currentUTC.getUTCDate(),
      14, // 14:00 UTC
      0, // Minutes
      0, // Seconds
    ),
  );

  // If current time is past 14:00 UTC, set next update to tomorrow
  if (currentUTC.getUTCHours() >= 14) {
    nextUpdate.setUTCDate(nextUpdate.getUTCDate() + 1);
  }

  const timeDiff = nextUpdate.getTime() - currentUTC.getTime();
  const hoursUntilUpdate = Math.floor(timeDiff / (1000 * 60 * 60));
  const minutesUntilUpdate = Math.floor(
    (timeDiff % (1000 * 60 * 60)) / (1000 * 60),
  );

  // Format the output
  if (hoursUntilUpdate > 0) {
    return `${hoursUntilUpdate}h ${minutesUntilUpdate}m`;
  } else if (minutesUntilUpdate > 0) {
    return `${minutesUntilUpdate}m`;
  } else {
    return "updating soon";
  }
}

// Add this helper function for relative time
function getTimeAgo(date: string): string {
  const now = new Date();
  const past = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  if (diffInSeconds < 60) return "just now";
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  return `${Math.floor(diffInSeconds / 86400)}d ago`;
}
