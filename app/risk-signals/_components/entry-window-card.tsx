import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import SettingsPanelDrawer from "./settings-panel-drawer";
import { TrendChange } from "@/db/trend-change";
import { Dispatch, SetStateAction, useState, useEffect } from "react";
import { DatePicker } from "./date-picker";
import TrendChangesFromPreviousDate from "./trend-changes-from-previous-date";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";
import { Suspense } from "react";
import { SpinnerBasic } from "@/components/spinner-basic";
import { RiskSignalSettings } from "@/types/user-profile";

interface EntryWindowCardProps {
  showStockPicksOnly: boolean;
  setShowStockPicksOnly: Dispatch<SetStateAction<boolean>>;
  sortStockPicksToTheTop: boolean;
  setSortStockPicksToTheTop: Dispatch<SetStateAction<boolean>>;
  showStocksOnly: boolean;
  setShowStocksOnly: Dispatch<SetStateAction<boolean>>;
  userId: string;
  entryWindow: string;
  handleEntryWindowChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  resultTableTrendChange: TrendChange[];
  stockPicks: TrendChange[];
  selectedTrendChangeDate: Date;
  onDateSelect: (date: Date | undefined) => void;
  initialWatchlistItems: string[];
  holdings: PortfolioPosition[];
  debouncedSaveSettings: (settings: RiskSignalSettings) => void;
}

export default function EntryWindowCard({
  showStockPicksOnly,
  setShowStockPicksOnly,
  sortStockPicksToTheTop,
  setSortStockPicksToTheTop,
  showStocksOnly,
  setShowStocksOnly,
  userId,
  entryWindow,
  handleEntryWindowChange,
  resultTableTrendChange,
  stockPicks,
  selectedTrendChangeDate,
  onDateSelect,
  initialWatchlistItems,
  holdings,
  debouncedSaveSettings,
}: EntryWindowCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // Any async operations here
      } catch (error) {
        console.error("Error initializing data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  return (
    <Suspense fallback={<SpinnerBasic />}>
      {isLoading ? (
        <SpinnerBasic />
      ) : (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-base">Entry Window</span>
                </div>
                <div className="flex items-center pl-4">
                  <SettingsPanelDrawer
                    showStockPicksOnly={showStockPicksOnly}
                    setShowStockPicksOnly={setShowStockPicksOnly}
                    sortStockPicksToTheTop={sortStockPicksToTheTop}
                    setSortStockPicksToTheTop={setSortStockPicksToTheTop}
                    showStocksOnly={showStocksOnly}
                    setShowStocksOnly={setShowStocksOnly}
                    userId={userId}
                    entryWindow={entryWindow}
                    debouncedSaveSettings={debouncedSaveSettings}
                  />
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center bg-background rounded-md border shadow-sm">
                    <Input
                      id="entry-window"
                      type="number"
                      value={entryWindow}
                      onChange={handleEntryWindowChange}
                      className="w-16 h-8 text-sm border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                      min="0"
                      max="100"
                    />
                    <span className="pr-2 text-sm text-muted-foreground">
                      %
                    </span>
                  </div>
                  <Badge variant="secondary" className="h-6">
                    Found{" "}
                    {
                      (showStockPicksOnly
                        ? resultTableTrendChange.filter((r) =>
                            stockPicks.some((s) => s.index === r.index),
                          )
                        : resultTableTrendChange
                      ).length
                    }
                  </Badge>
                </div>
                <DatePicker
                  date={selectedTrendChangeDate}
                  onSelect={onDateSelect}
                />
              </div>

              <div className="flex justify-end">
                <TrendChangesFromPreviousDate
                  selectedTrendChangeDate={selectedTrendChangeDate}
                  initialWatchlistItems={initialWatchlistItems}
                  userId={userId}
                  holdings={holdings}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </Suspense>
  );
}
