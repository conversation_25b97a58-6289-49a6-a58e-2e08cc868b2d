"use client";

import React, {
  useState,
  useEffect,
  Dispatch,
  SetStateAction,
  useMemo,
} from "react";
import { Drawer, DrawerContent, DrawerTrigger, DrawerTitle } from "@/components/ui/drawer";
import { GearIcon } from "@radix-ui/react-icons";
import SettingsPanel from "./settings-panel";
import { Button } from "@/components/ui/button";
import { RiskSignalSettings, UserProfileSettings } from "@/types/user-profile";
import { toast } from "@/hooks/use-toast";
import { debounce } from "lodash";

interface SettingsPanelDrawerProps {
  showStockPicksOnly: boolean;
  setShowStockPicksOnly: Dispatch<SetStateAction<boolean>>;
  sortStockPicksToTheTop: boolean;
  setSortStockPicksToTheTop: Dispatch<SetStateAction<boolean>>;
  showStocksOnly: boolean;
  setShowStocksOnly: Dispatch<SetStateAction<boolean>>;
  userId: string;
  entryWindow: string;
  debouncedSaveSettings: (settings: RiskSignalSettings) => void;
}

export default function SettingsPanelDrawer({
  showStockPicksOnly,
  setShowStockPicksOnly,
  sortStockPicksToTheTop,
  setSortStockPicksToTheTop,
  showStocksOnly,
  setShowStocksOnly,
  userId,
  entryWindow,
  debouncedSaveSettings,
}: SettingsPanelDrawerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [temporarySettings, setTemporarySettings] =
    useState<RiskSignalSettings>(() => ({
      defaultEntryWindow: Number(entryWindow),
      showStockPicksOnly,
      sortStockPicksToTop: sortStockPicksToTheTop,
      showStockInstrumentsOnly: showStocksOnly,
    }));

  const handleSubmit = async () => {
    try {
      // Update parent state synchronously
      setShowStockPicksOnly(temporarySettings.showStockPicksOnly);
      setSortStockPicksToTheTop(temporarySettings.sortStockPicksToTop);
      setShowStocksOnly(temporarySettings.showStockInstrumentsOnly);

      // Close drawer after state updates
      setIsOpen(false);

      // Use the debounced save function
      debouncedSaveSettings(temporarySettings);
    } catch (error) {
      console.error("Settings Update Error:", error);
      toast({
        title: "Failed to save settings",
        variant: "destructive",
      });
    }
  };

  // Handle individual setting changes
  const handleSettingChange = (settings: RiskSignalSettings) => {
    setTemporarySettings(settings);
    debouncedSaveSettings(settings);
  };

  // Add an onOpenChange handler
  const handleOpenChange = (open: boolean) => {
    console.log("Drawer Open Change:", {
      open,
      currentSettings: {
        showStockPicksOnly,
        sortStockPicksToTheTop,
        showStocksOnly,
      },
    });

    if (open) {
      setTemporarySettings({
        defaultEntryWindow: Number(entryWindow),
        showStockPicksOnly,
        sortStockPicksToTop: sortStockPicksToTheTop,
        showStockInstrumentsOnly: showStocksOnly,
      });
    }
    setIsOpen(open);
  };

  return (
    <Drawer open={isOpen} onOpenChange={handleOpenChange}>
      <DrawerTrigger asChild>
        <Button variant="outline" size="icon" className="relative group">
          <GearIcon className="h-4 w-4 transition-transform group-hover:rotate-180 duration-300" />
          <span className="sr-only">More settings</span>
          <div className="absolute -bottom-8 scale-0 group-hover:scale-100 transition-transform bg-secondary text-secondary-foreground text-xs px-2 py-1 rounded whitespace-nowrap z-50">
            More settings
          </div>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="h-[80vh]">
        <DrawerTitle className="sr-only">Risk Signal Settings</DrawerTitle>
        <div className="mx-auto w-full max-w-sm h-full overflow-y-auto">
          <SettingsPanel
            userId={userId}
            temporarySettings={temporarySettings}
            onSettingsChange={(settings: RiskSignalSettings) =>
              handleSettingChange(settings)
            }
          />
          <div className="flex justify-end gap-2 p-4 mt-4 border-t">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>Apply Changes</Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
