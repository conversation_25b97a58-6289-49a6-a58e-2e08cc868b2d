"use client";

import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { BellIcon, BellOffIcon, InfoIcon } from "lucide-react";
import { subscribeToPushNotifications } from "@/utils/notifications";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SpinnerBasic } from "@/components/spinner-basic";
import { isPWAInstalled } from "@/utils/pwa";

interface NotificationPreferencesProps {
  userId: string;
}

export function NotificationPreferences({
  userId,
}: NotificationPreferencesProps) {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPWA, setIsPWA] = useState(false);

  useEffect(() => {
    setIsPWA(isPWAInstalled());
  }, []);

  const checkSubscriptionStatus = async () => {
    try {
      if (!("Notification" in window)) {
        setError("Notifications are not supported in this browser");
        return;
      }

      const permission = Notification.permission;
      if (permission === "granted") {
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.getSubscription();
        setIsSubscribed(!!subscription);
      } else {
        setIsSubscribed(false);
      }
    } catch (err) {
      setError("Failed to check notification status");
      console.error("Error checking notification status:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnsubscribe = async () => {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();

    if (subscription) {
      try {
        // First, remove from database
        await fetch("/api/push/unsubscribe", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ endpoint: subscription.endpoint }),
        });

        // Then, unsubscribe from push manager
        await subscription.unsubscribe();
        setIsSubscribed(false);
      } catch (error) {
        throw new Error("Failed to unsubscribe from notifications");
      }
    }
  };

  const handleToggleSubscription = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!isPWA) {
        throw new Error(
          "Please install Lunar Hedge as a PWA before enabling notifications",
        );
      }

      if (!isSubscribed) {
        await subscribeToPushNotifications().catch((error) => {
          throw new Error(
            error instanceof Error ? error.message : "Subscription failed",
          );
        });
      } else {
        await handleUnsubscribe();
      }

      await checkSubscriptionStatus();
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to manage notification subscription",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      const response = await fetch("/api/push/test");
      if (!response.ok) {
        throw new Error("Failed to send test notification");
      }
      console.log("Test notification sent");
    } catch (error) {
      setError("Failed to send test notification");
    }
  };

  useEffect(() => {
    checkSubscriptionStatus().finally(() => {
      setIsLoading(false);
    });
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <SpinnerBasic />
        <span className="ml-2">Loading notification preferences...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="rounded-lg border border-blue-100 bg-blue-50 p-4">
        <div className="flex items-start">
          <InfoIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-blue-900">
              Setting Up Push Notifications
            </h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p>Before enabling notifications, please ensure:</p>
              <ol className="list-decimal ml-4 space-y-1">
                <li>
                  You have installed Lunar Hedge as a PWA (Progressive Web App)
                  on your device
                </li>
                <li>
                  Your browser permissions allow notifications for this website
                </li>
                <li>
                  You are using a supported browser (Chrome, Edge, Firefox, or
                  Safari)
                </li>
              </ol>
              <p className="mt-2">
                When you toggle notifications on, your browser will prompt you
                to allow notifications. You must accept this prompt to receive
                updates.
              </p>
            </div>
            {Notification.permission === "denied" && (
              <div className="mt-2 text-sm text-red-600">
                ⚠️ Notifications are currently blocked. Please enable them in
                your browser settings to receive updates.
              </div>
            )}
            {!isPWA && (
              <div className="mt-2 text-sm text-amber-600">
                ⚠️ Please install Lunar Hedge as a PWA to enable notifications.
                See installation instructions below.
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-medium text-slate-900">
            Push Notifications
          </h3>
          <p className="text-sm text-slate-500">
            Receive updates when risk signals data changes
          </p>
        </div>
        <Switch
          checked={isSubscribed}
          onCheckedChange={handleToggleSubscription}
          disabled={isLoading || !isPWA}
        />
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="bg-slate-50 rounded-lg p-4 space-y-4">
        <div className="flex items-center space-x-3">
          {isSubscribed ? (
            <>
              <BellIcon className="h-5 w-5 text-green-500" />
              <span className="text-sm text-slate-700">
                You will receive notifications when risk signals are updated
              </span>
            </>
          ) : (
            <>
              <BellOffIcon className="h-5 w-5 text-slate-400" />
              <span className="text-sm text-slate-500">
                Enable notifications to stay updated with risk signal changes
              </span>
            </>
          )}
        </div>

        <div className="text-xs text-slate-500">
          {isSubscribed ? (
            <p>
              You can disable notifications at any time by toggling the switch
              above or through your browser settings.
            </p>
          ) : (
            <p>
              You will need to allow notifications in your browser when prompted
              after enabling this setting.
            </p>
          )}
        </div>
      </div>

      {!isSubscribed && (
        <div className="text-sm text-slate-600 bg-slate-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">How to Install Lunar Hedge:</h4>
          <div className="space-y-2">
            <p className="font-medium">On iOS Safari:</p>
            <ol className="list-decimal ml-4 space-y-1">
              <li>Tap the Share button</li>
              <li>Scroll down and tap "Add to Home Screen"</li>
              <li>Tap "Add" to confirm</li>
            </ol>

            <p className="font-medium mt-3">On Android Chrome:</p>
            <ol className="list-decimal ml-4 space-y-1">
              <li>Tap the menu (three dots)</li>
              <li>Tap "Add to Home screen"</li>
              <li>Tap "Install" to confirm</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}
