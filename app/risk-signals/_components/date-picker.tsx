"use client";

import * as React from "react";
import { format, isWeekend } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerProps {
  date: Date;
  onSelect: (date: Date | undefined) => void;
}

export function DatePicker({ date, onSelect }: DatePickerProps) {
  const isDateDisabled = (date: Date) => {
    return date > new Date() || isWeekend(date);
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      // Create date in UTC to avoid timezone offset issues
      const year = selectedDate.getFullYear();
      const month = selectedDate.getMonth();
      const day = selectedDate.getDate();

      // Create new UTC date
      const normalizedDate = new Date(Date.UTC(year, month, day, 12, 0, 0));

      onSelect(normalizedDate);
    } else {
      onSelect(undefined);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative group">
          <div className="flex items-center justify-center gap-2">
            {format(date, "MM/dd/yyyy")}
            <CalendarIcon className="h-4 w-4" />
          </div>
          <span className="sr-only">Open date picker</span>
          <div className="absolute -bottom-8 scale-0 group-hover:scale-100 transition-transform bg-secondary text-secondary-foreground text-xs px-2 py-1 rounded whitespace-nowrap">
            Change date
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="end">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          disabled={isDateDisabled}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
