"use client";
import React, { useState } from "react";
import { testIBKRConnection } from "./actions/ibkr-test-connection";
import {
  fetchIBKRAccountInfo,
  type AccountInfo,
} from "./actions/ibkr-fetch-account";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { CheckCircle2, XCircle, Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import { getUserProfile } from "@/db/user-profile";
import { useSession } from "next-auth/react";
import { toast } from "@/hooks/use-toast";
import { IBKRAccountSettings } from "./_components/ibkr-account-settings";

export function IBAccountDetails() {
  const { data: session } = useSession();
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);
  const [isLoadingAccount, setIsLoadingAccount] = useState(false);

  const handleTestConnection = async () => {
    try {
      setIsConnecting(true);
      setConnectionStatus("idle");

      // Get connection details from user profile
      const profile = await getUserProfile(session?.user?.id!);
      const connectionDetails = profile?.settings?.ibkrConnectionDetail;

      if (!connectionDetails) {
        toast({
          title: "Error",
          description: "Please configure your IBKR connection details first.",
          variant: "destructive",
        });
        return;
      }

      const result = await testIBKRConnection(session?.user?.id!);
      setConnectionStatus(result ? "success" : "error");

      // if (result) {
      //   console.log("Verifying connection success, fetching account info");
      //   setIsLoadingAccount(true);
      //   const info = await fetchIBKRAccountInfo(session?.user?.id!);
      //   console.log("info: ", info);
      //   setAccountInfo(info);
      // }
    } catch (error) {
      setConnectionStatus("error");
      console.error("Failed to test IBKR connection:", error);
    } finally {
      setIsConnecting(false);
      setIsLoadingAccount(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-base md:text-lg space-y-4">
        <div className="flex flex-col items-center gap-4">
          <Button
            onClick={handleTestConnection}
            disabled={isConnecting}
            variant="outline"
            className="w-[200px]"
          >
            {isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify IBKR Connection"
            )}
          </Button>

          {connectionStatus !== "idle" && (
            <div className="flex items-center gap-2">
              {connectionStatus === "success" ? (
                <>
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                  <span className="text-green-600 font-medium">
                    IBKR connection available
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="text-red-600 font-medium">
                    Unable to reach IBKR
                  </span>
                </>
              )}
            </div>
          )}

          {isLoadingAccount && (
            <div className="w-full max-w-md">
              <Skeleton className="h-[100px] w-full" />
            </div>
          )}

          {accountInfo && (
            <Card className="w-full max-w-md p-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Account ID:</span>
                  <span className="font-medium">{accountInfo.accountId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Available Funds:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.availableFunds.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Net Liquidation:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.netLiquidation.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Total Cash Value:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.totalCashValue.toLocaleString()}
                  </span>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>

      <div className="border-t border-slate-200 dark:border-slate-800" />

      <div className="space-y-4">
        <h3 className="text-base md:text-lg font-medium text-slate-900">
          Account Settings
        </h3>
        <IBKRAccountSettings />
      </div>

      {accountInfo && (
        <>
          <div className="border-t border-slate-200 dark:border-slate-800" />
          <div className="space-y-4">
            <h3 className="text-base md:text-lg font-medium text-slate-900">
              Account Information
            </h3>
            <Card className="w-full p-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Account ID:</span>
                  <span className="font-medium">{accountInfo.accountId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Available Funds:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.availableFunds.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Net Liquidation:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.netLiquidation.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Total Cash Value:
                  </span>
                  <span className="font-medium">
                    ${accountInfo.totalCashValue.toLocaleString()}
                  </span>
                </div>
              </div>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
