import { TrendChange } from "@/db/trend-change";
import { Card } from "@/components/ui/card";
import { Text, Badge, ScrollArea } from "@radix-ui/themes";
import { ArrowUpIcon, ArrowDownIcon, DashIcon } from "@radix-ui/react-icons";
import { customNumberFormatter } from "@/lib/utils-client";
import OptionChainDialog from "./option-chain";
import OrderDialog from "@/app/portfolio/_components/order-dialog";
import WatchlistButton from "./watchlist-button";
import { HoldingsIndicator } from "@/app/_components/holdings-indicator";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";

interface MobileCardViewProps {
  resultTableTrendChange: TrendChange[];
  showStockPicksOnly: boolean;
  stockPicks: TrendChange[];
  entryWindow: string;
  session: any;
  watchlistItems: string[];
  onToggleWatchlist: (symbol: string) => Promise<void>;
  holdings: PortfolioPosition[] | null;
}

export default function MobileCardView({
  resultTableTrendChange,
  showStockPicksOnly,
  stockPicks,
  entryWindow,
  session,
  watchlistItems,
  onToggleWatchlist,
  holdings,
}: MobileCardViewProps) {
  return (
    <div className="block md:hidden">
      <ScrollArea className="h-[calc(100vh-300px)]">
        <div className="p-4">
          <div className="grid grid-cols-1 gap-4">
            {resultTableTrendChange.map((r) => (
              <Card
                key={r.index}
                className={`
                    ${
                      stockPicks.some((s) => s.index === r.index)
                        ? "dark:bg-green-900/40 bg-green-50"
                        : ""
                    }
                    hover:dark:bg-slate-800 hover:bg-slate-50
                    transition-colors
                  `}
              >
                <div className="p-4 space-y-4">
                  {/* Header with Symbol and Trend */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Text weight="bold" size="5">
                        {r.index}
                      </Text>
                      <HoldingsIndicator symbol={r.index} holdings={holdings} />
                      <Badge
                        variant="soft"
                        color={
                          r.trend === "BULLISH"
                            ? "green"
                            : r.trend === "BEARISH"
                              ? "red"
                              : "blue"
                        }
                        className="flex items-center gap-1"
                      >
                        {r.trend === "BULLISH" ? (
                          <ArrowUpIcon />
                        ) : r.trend === "BEARISH" ? (
                          <ArrowDownIcon />
                        ) : (
                          <DashIcon />
                        )}
                        {r.trend}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      {session.user.role === "admin" &&
                        stockPicks.some((s) => s.index === r.index) && (
                          <div className="flex items-center gap-2">
                            <OrderDialog
                              userId={session.user.id}
                              symbol={r.index}
                              price={r.previousClose}
                              action={r.trend === "BULLISH" ? "BUY" : "SELL"}
                            />
                          </div>
                        )}
                      <WatchlistButton
                        symbol={r.index}
                        isInWatchlist={watchlistItems.includes(r.index)}
                        onToggleWatchlist={onToggleWatchlist}
                      />
                    </div>
                  </div>

                  {/* Price Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <Text size="1" color="gray">
                        Buy
                      </Text>
                      <Text weight="medium">
                        {customNumberFormatter(r.buyTrade)}
                      </Text>
                    </div>
                    <div className="space-y-1">
                      <Text size="1" color="gray">
                        Sell
                      </Text>
                      <Text weight="medium">
                        {customNumberFormatter(r.sellTrade)}
                      </Text>
                    </div>
                    <div className="space-y-1">
                      <Text size="1" color="gray">
                        Previous
                      </Text>
                      <Text weight="medium">
                        {customNumberFormatter(r.previousClose)}
                      </Text>
                    </div>
                    <div className="space-y-1">
                      <Text size="1" color="gray">
                        Window
                      </Text>
                      <Text weight="medium">
                        {customNumberFormatter(
                          (r.sellTrade - r.buyTrade) *
                            (Number(entryWindow) / 100),
                        )}
                      </Text>
                    </div>
                  </div>

                  {/* Optional: Add a visual indicator for the price range */}
                  <div className="relative h-2 bg-slate-100 dark:bg-slate-800 rounded-full overflow-hidden">
                    <div
                      className="absolute h-full bg-blue-500"
                      style={{
                        left: `${(r.buyTrade / r.sellTrade) * 100}%`,
                        right: `${
                          100 - (r.previousClose / r.sellTrade) * 100
                        }%`,
                      }}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
